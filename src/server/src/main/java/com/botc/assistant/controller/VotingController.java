package com.botc.assistant.controller;

import com.botc.assistant.model.Game;
import com.botc.assistant.model.Player;
import com.botc.assistant.model.Vote;
import com.botc.assistant.service.GameService;
import com.botc.assistant.service.VotingService;
import com.botc.assistant.repository.PlayerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.HashMap;

/**
 * REST controller for voting operations.
 */
@RestController
@RequestMapping("/api/games/{gameId}/voting")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:5174", "http://localhost:3000" })
public class VotingController {

    @Autowired
    private VotingService votingService;

    @Autowired
    private GameService gameService;

    @Autowired
    private PlayerRepository playerRepository;

    /**
     * Start a voting session (nominate a player).
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startVoting(@PathVariable String gameId,
            @RequestBody StartVotingRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // For demo purposes, create a simple mock vote response
            response.put("success", true);
            response.put("message", "Nomination started successfully");
            response.put("gameId", gameId);
            response.put("nominatorId", request.getNominatorId());
            response.put("nominatedPlayerId", request.getNominatedPlayerId());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", "Error starting nomination: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Cast a vote in the current voting session.
     */
    @PostMapping("/vote")
    public ResponseEntity<Map<String, Object>> castVote(@PathVariable String gameId,
            @RequestBody CastVoteRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // For demo purposes, create a simple mock vote response
            response.put("success", true);
            response.put("message", "Vote cast successfully");
            response.put("gameId", gameId);
            response.put("playerId", request.getPlayerId());
            response.put("choice", request.getChoice());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", "Error casting vote: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * End the current voting session.
     */
    @PostMapping("/end")
    public ResponseEntity<Map<String, Object>> endVoting(@PathVariable String gameId) {
        Map<String, Object> response = new HashMap<>();

        try {
            // For demo purposes, create a simple mock response
            response.put("success", true);
            response.put("message", "Voting ended successfully");
            response.put("gameId", gameId);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", "Error ending vote: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Get the current active vote.
     */
    @GetMapping("/current")
    public ResponseEntity<Vote> getCurrentVote(@PathVariable String gameId) {
        try {
            Optional<Vote> vote = votingService.getCurrentVote(gameId);
            return vote.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get all votes for a game.
     */
    @GetMapping
    public ResponseEntity<List<Vote>> getGameVotes(@PathVariable String gameId) {
        try {
            List<Vote> votes = votingService.getGameVotes(gameId);
            return ResponseEntity.ok(votes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Cancel the current vote.
     */
    @PostMapping("/cancel")
    public ResponseEntity<Void> cancelVote(@PathVariable String gameId) {
        try {
            Optional<Game> gameOpt = gameService.findById(gameId);
            if (gameOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            votingService.cancelCurrentVote(gameOpt.get());
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Request DTOs
    public static class StartVotingRequest {
        private String nominatorId;
        private String nominatedPlayerId;

        // Getters and setters
        public String getNominatorId() {
            return nominatorId;
        }

        public void setNominatorId(String nominatorId) {
            this.nominatorId = nominatorId;
        }

        public String getNominatedPlayerId() {
            return nominatedPlayerId;
        }

        public void setNominatedPlayerId(String nominatedPlayerId) {
            this.nominatedPlayerId = nominatedPlayerId;
        }
    }

    public static class CastVoteRequest {
        private String playerId;
        private String choice; // FOR, AGAINST, ABSTAIN

        // Getters and setters
        public String getPlayerId() {
            return playerId;
        }

        public void setPlayerId(String playerId) {
            this.playerId = playerId;
        }

        public String getChoice() {
            return choice;
        }

        public void setChoice(String choice) {
            this.choice = choice;
        }
    }
}
