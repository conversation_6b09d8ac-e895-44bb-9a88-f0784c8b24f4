import { useState, useEffect } from 'react';
import './App.css';
import GameTimer from './components/GameTimer';
import RoleCard from './components/RoleCard';
import { registerUser, loginUser, logoutUser, onAuthStateChange, getCurrentUser, getUserToken } from './firebase/auth';
import { getUsers } from './firebase/firestore';

const API_BASE_URL = 'http://localhost:8080/api';

function App() {
  // State management
  const [currentUser, setCurrentUser] = useState(null);
  const [currentGame, setCurrentGame] = useState(null);
  const [games, setGames] = useState([]);
  const [players, setPlayers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('auth');

  // Form states
  const [authForm, setAuthForm] = useState({ username: '', email: '', password: '', isStoryteller: false });
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [gameForm, setGameForm] = useState({ name: '', maxPlayers: 10 });
  const [joinForm, setJoinForm] = useState({ gameId: '' });

  // Timer and game state
  const [timerRunning, setTimerRunning] = useState(false);
  const [currentPhase, setCurrentPhase] = useState('day');
  const [timerDuration, setTimerDuration] = useState(300);

  // Utility functions
  const showError = (message) => {
    // Check if it's a Firebase blocking error and show helpful message
    if (message.includes('blocked by your browser') || message.includes('ERR_BLOCKED_BY_CLIENT')) {
      setError('🚫 Firebase is blocked by your ad blocker! Please disable your ad blocker for this site or add *.googleapis.com to your allowlist.');
    } else {
      setError(message);
    }
    setSuccess('');
    setTimeout(() => setError(''), 8000); // Longer timeout for blocking errors
  };

  const showSuccess = (message) => {
    setSuccess(message);
    setError('');
    setTimeout(() => setSuccess(''), 3000);
  };

  // Firebase authentication listener and connection test
  useEffect(() => {
    const unsubscribe = onAuthStateChange((user) => {
      setCurrentUser(user);
      if (user) {
        showSuccess(`Welcome back, ${user.username}!`);
      }
    });

    // Test Firebase connection on app load
    const testFirebaseConnection = async () => {
      try {
        await getUsers(); // Simple read test
      } catch (error) {
        if (error.message.includes('blocked by your browser')) {
          showError(error.message);
        }
      }
    };

    testFirebaseConnection();
    return () => unsubscribe();
  }, []);

  const apiCall = async (endpoint, options = {}) => {
    setLoading(true);
    try {
      // Get Firebase ID token for authentication
      const token = await getUserToken();
      const headers = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers,
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(errorData || `HTTP ${response.status}`);
      }

      // Check if response has content before trying to parse JSON
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');

      if (contentLength === '0' || !contentType?.includes('application/json')) {
        // Empty response or non-JSON response
        return null;
      }

      const data = await response.json();
      return data;
    } catch (err) {
      showError(`API Error: ${err.message}`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // API Handler Functions
  const handleRegister = async (e) => {
    e.preventDefault();
    try {
      const result = await registerUser(
        authForm.username,
        authForm.email,
        authForm.password,
        authForm.isStoryteller
      );

      if (result.success) {
        setCurrentUser(result.user);
        const roleText = result.user.userRole === 'STORYTELLER' ? 'Storyteller' : 'Player';
        showSuccess(`${roleText} ${result.user.username} registered successfully!`);
        setAuthForm({ username: '', email: '', password: '', isStoryteller: false });
      } else {
        showError(result.message);
      }
      loadUsers(); // Refresh user list
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleLogin = async (e) => {
    e.preventDefault();

    try {
      // For Firebase login, we need email. For demo purposes, we'll use the username as email
      // In a real app, you'd have a separate email field or lookup email by username
      const email = loginForm.username.includes('@') ? loginForm.username : `${loginForm.username}@example.com`;

      const result = await loginUser(loginForm.username, email, loginForm.password);

      if (result.success) {
        setCurrentUser(result.user);
        showSuccess(`Welcome back, ${result.user.username}!`);
        setLoginForm({ username: '', password: '' });
      } else {
        showError(result.message);
      }
    } catch (error) {
      showError('Login failed. Please try again.');
    }
  };

  const handleSelectUser = (user) => {
    setCurrentUser(user);
    showSuccess(`Now acting as ${user.username} (${user.userRole})`);
  };

  const handleLogout = async () => {
    try {
      const result = await logoutUser();
      if (result.success) {
        setCurrentUser(null);
        showSuccess('Logged out successfully');
      } else {
        showError('Logout failed');
      }
    } catch (error) {
      showError('Logout failed');
    }
  };

  const loadUsers = async () => {
    try {
      const users = await getUsers();
      setUsers(users);
      showSuccess(`Loaded ${users.length} users`);
    } catch (error) {
      showError('Failed to load users');
    }
  };



  const handleCreateGame = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      showError('Please register a user first');
      return;
    }

    if (currentUser.userRole !== 'STORYTELLER') {
      showError('Only storytellers can create games. Please register as a storyteller.');
      return;
    }

    try {
      const game = await apiCall('/games', {
        method: 'POST',
        body: JSON.stringify({
          ...gameForm,
          storytellerId: currentUser.id,
        }),
      });
      setCurrentGame(game);
      showSuccess(`Game "${game.name}" created successfully!`);
      setGameForm({ name: '', maxPlayers: 10 });
      handleLoadGames(); // Refresh games list
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleJoinGame = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      showError('Please select a user first');
      return;
    }

    if (!joinForm.gameId.trim()) {
      showError('Please enter a game ID');
      return;
    }

    const gameId = joinForm.gameId.trim();

    try {
      const player = await apiCall(`/games/${gameId}/join`, {
        method: 'POST',
        body: JSON.stringify({
          userId: currentUser.id,
        }),
      });
      showSuccess(`Joined game as ${player.name}!`);
      setJoinForm({ gameId: '' });

      // Load the game details using the saved gameId (not the cleared form)
      try {
        const game = await apiCall(`/games/${gameId}`);
        setCurrentGame(game);
        handleLoadPlayers(gameId);

        // Refresh the games list to show updated player count
        handleLoadGames();
      } catch (gameLoadError) {
        // Game joined successfully but couldn't load details - that's okay
        console.warn('Could not load game details after joining:', gameLoadError);
      }
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleLoadGames = async () => {
    try {
      const gamesList = await apiCall('/games');
      setGames(gamesList);
      showSuccess('Games loaded successfully!');
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleLoadPlayers = async (gameId) => {
    if (!gameId && !currentGame?.id) return;
    try {
      const playersList = await apiCall(`/games/${gameId || currentGame.id}/players`);
      setPlayers(playersList);
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleAssignRoles = async () => {
    if (!currentGame) return;
    try {
      const result = await apiCall(`/games/${currentGame.id}/assign-roles`, {
        method: 'POST',
      });

      if (result && result.success) {
        showSuccess(`Roles assigned successfully! ${result.playersCount} players, ${result.rolesAssigned} roles assigned.`);
      } else {
        showSuccess('Roles assigned successfully!');
      }

      // Refresh game and players data
      const updatedGame = await apiCall(`/games/${currentGame.id}`);
      setCurrentGame(updatedGame);
      handleLoadPlayers();
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleStartGame = async () => {
    if (!currentGame) return;
    try {
      const startedGame = await apiCall(`/games/${currentGame.id}/start`, {
        method: 'POST',
        body: JSON.stringify({
          storytellerId: currentUser.id
        }),
      });
      setCurrentGame(startedGame);
      showSuccess('Game started successfully!');
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleLoadRoles = async () => {
    try {
      const rolesList = await apiCall('/roles');
      setRoles(rolesList);
      showSuccess('Roles loaded successfully!');
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleStartVoting = async (playerId) => {
    if (!currentGame || !currentUser) return;

    try {
      const result = await apiCall(`/games/${currentGame.id}/voting/start`, {
        method: 'POST',
        body: JSON.stringify({
          nominatorId: currentUser.id,  // Use user ID for simplicity
          nominatedPlayerId: playerId
        }),
      });

      if (result && result.success) {
        showSuccess(result.message || 'Voting started!');
      } else {
        showSuccess('Voting started!');
      }
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleCastVote = async (choice) => {
    if (!currentGame || !currentUser) return;

    try {
      const result = await apiCall(`/games/${currentGame.id}/voting/vote`, {
        method: 'POST',
        body: JSON.stringify({
          playerId: currentUser.id,  // Use user ID for simplicity
          choice: choice
        }),
      });

      if (result && result.success) {
        showSuccess(result.message || `Vote cast: ${choice}`);
      } else {
        showSuccess(`Vote cast: ${choice}`);
      }
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  const handleEndVoting = async () => {
    if (!currentGame) return;
    try {
      const result = await apiCall(`/games/${currentGame.id}/voting/end`, {
        method: 'POST',
      });

      if (result && result.success) {
        showSuccess(result.message || 'Voting ended!');
      } else {
        showSuccess('Voting ended!');
      }
    } catch (error) {
      // Error already handled by apiCall
    }
  };

  return (
    <div className="app testing-interface">
      <header className="testing-header">
        <h1>🧪 Blood on the Clocktower - Backend Testing Interface</h1>
        <div className="status-bar">
          {currentUser && <span className="user-status">👤 {currentUser.username}</span>}
          {currentGame && <span className="game-status">🎮 {currentGame.name}</span>}
          {loading && <span className="loading-status">⏳ Loading...</span>}
        </div>
      </header>

      {/* Error/Success Messages */}
      {error && (
        <div className="alert alert-error">
          ❌ {error}
          {error.includes('blocked by your browser') && (
            <div style={{ marginTop: '10px', fontSize: '0.9em' }}>
              <strong>Quick fixes:</strong>
              <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                <li>Disable ad blocker for this site</li>
                <li>Add *.googleapis.com to your allowlist</li>
                <li>Try incognito/private mode</li>
              </ul>
            </div>
          )}
        </div>
      )}
      {success && <div className="alert alert-success">✅ {success}</div>}

      {/* Navigation Tabs */}
      <nav className="tab-navigation">
        {['auth', 'games', 'roles', 'voting', 'timer'].map(tab => (
          <button
            key={tab}
            className={`tab-button ${activeTab === tab ? 'active' : ''}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab === 'auth' && '🔐 Authentication'}
            {tab === 'games' && '🎮 Games'}
            {tab === 'roles' && '👥 Roles'}
            {tab === 'voting' && '🗳️ Voting'}
            {tab === 'timer' && '⏰ Timer'}
          </button>
        ))}
      </nav>

      <main className="testing-content">
        {/* Authentication Tab */}
        {activeTab === 'auth' && (
          <div className="tab-content">
            <h2>🔐 User Authentication Testing</h2>

            <div className="form-section">
              <h3>Login Existing User</h3>
              <form onSubmit={handleLogin} className="test-form">
                <input
                  type="text"
                  placeholder="Username"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                  required
                />
                <input
                  type="password"
                  placeholder="Password"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                  required
                />
                <button type="submit" disabled={loading}>Login</button>
              </form>
            </div>

            <div className="form-section">
              <h3>Register New User</h3>
              <form onSubmit={handleRegister} className="test-form">
                <input
                  type="text"
                  placeholder="Username"
                  value={authForm.username}
                  onChange={(e) => setAuthForm({ ...authForm, username: e.target.value })}
                  required
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={authForm.email}
                  onChange={(e) => setAuthForm({ ...authForm, email: e.target.value })}
                  required
                />
                <input
                  type="password"
                  placeholder="Password"
                  value={authForm.password}
                  onChange={(e) => setAuthForm({ ...authForm, password: e.target.value })}
                  required
                />
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={authForm.isStoryteller}
                    onChange={(e) => setAuthForm({ ...authForm, isStoryteller: e.target.checked })}
                  />
                  Register as Storyteller
                </label>
                <button type="submit" disabled={loading}>Register User</button>
              </form>
            </div>



            <div className="form-section">
              <h3>Current User Info</h3>
              {currentUser ? (
                <div className="user-info">
                  <p><strong>ID:</strong> {currentUser.id}</p>
                  <p><strong>Username:</strong> {currentUser.username}</p>
                  <p><strong>Email:</strong> {currentUser.email}</p>
                  <p><strong>Role:</strong> {currentUser.userRole}</p>
                  <p><strong>Is Storyteller:</strong> {currentUser.userRole === 'STORYTELLER' ? 'Yes' : 'No'}</p>
                  <p><strong>Games Played:</strong> {currentUser.gamesPlayed}</p>
                  <p><strong>Games Won:</strong> {currentUser.gamesWon}</p>
                  <p><strong>Win Rate:</strong> {(currentUser.winRate * 100).toFixed(1)}%</p>
                  <button onClick={handleLogout} className="logout-btn">Logout</button>
                </div>
              ) : (
                <p>No user logged in</p>
              )}
            </div>

            <div className="form-section">
              <h3>All Registered Users</h3>
              <button onClick={loadUsers} disabled={loading}>Load Users</button>
              {users.length > 0 ? (
                <div className="users-list">
                  <p className="users-hint">💡 Click on any user to "become" them for testing</p>
                  {users.map(user => (
                    <div
                      key={user.id}
                      className={`user-card clickable ${currentUser?.id === user.id ? 'current-user' : ''}`}
                      onClick={() => handleSelectUser(user)}
                      title="Click to become this user"
                    >
                      <div className="user-header">
                        <strong>{user.username}</strong>
                        <span className={`role-badge ${user.userRole.toLowerCase()}`}>
                          {user.userRole}
                        </span>
                        {currentUser?.id === user.id && <span className="current-badge">CURRENT</span>}
                      </div>
                      <p className="user-email">{user.email}</p>
                      <div className="user-stats">
                        <span>🎮 Games: {user.gamesPlayed}</span>
                        <span>🏆 Won: {user.gamesWon}</span>
                        <span>📊 Win Rate: {(user.winRate * 100).toFixed(1)}%</span>
                        {user.userRole === 'STORYTELLER' && (
                          <span>🎭 Storyteller Games: {user.gamesAsStoryteller}</span>
                        )}
                      </div>
                      <p className="user-id">ID: {user.id.substring(0, 8)}...</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No users loaded. Click "Load Users" to see registered users.</p>
              )}
            </div>
          </div>
        )}

        {/* Games Tab */}
        {activeTab === 'games' && (
          <div className="tab-content">
            <h2>🎮 Game Management Testing</h2>

            <div className="form-section">
              <h3>Create Game</h3>
              <form onSubmit={handleCreateGame} className="test-form">
                <input
                  type="text"
                  placeholder="Game Name"
                  value={gameForm.name}
                  onChange={(e) => setGameForm({ ...gameForm, name: e.target.value })}
                  required
                />
                <input
                  type="number"
                  placeholder="Max Players"
                  min="5"
                  max="15"
                  value={gameForm.maxPlayers}
                  onChange={(e) => setGameForm({ ...gameForm, maxPlayers: parseInt(e.target.value) })}
                  required
                />
                <button type="submit" disabled={loading || currentUser?.userRole !== 'STORYTELLER'}>
                  Create Game
                </button>
              </form>
            </div>

            <div className="form-section">
              <h3>Join Game</h3>
              <form onSubmit={handleJoinGame} className="test-form">
                <div className="game-selection">
                  <label>Select Game:</label>
                  {games.length > 0 ? (
                    <select
                      value={joinForm.gameId}
                      onChange={(e) => setJoinForm({ ...joinForm, gameId: e.target.value })}
                      required
                    >
                      <option value="">Choose a game...</option>
                      {games.map(game => (
                        <option key={game.id} value={game.id}>
                          {game.name} (ID: {game.id.substring(0, 8)}...) - {game.players?.length || 0}/{game.maxPlayers} players
                          {game.players && game.players.length > 0 && ` [${game.players.map(p => p.name).join(', ')}]`}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div>
                      <input
                        type="text"
                        placeholder="Game ID (or click 'Load Games' first)"
                        value={joinForm.gameId}
                        onChange={(e) => setJoinForm({ ...joinForm, gameId: e.target.value })}
                        required
                      />
                      <p className="hint">💡 Click "Load Games" below to see available games in dropdown</p>
                    </div>
                  )}
                </div>
                <p className="info">You will join as: <strong>{currentUser?.username || 'Not logged in'}</strong></p>
                <button type="submit" disabled={loading || !currentUser}>
                  Join Game
                </button>
              </form>
            </div>

            <div className="form-section">
              <h3>Game Actions</h3>
              <div className="button-group">
                <button onClick={handleLoadGames} disabled={loading}>
                  Load All Games
                </button>
                <button onClick={handleAssignRoles} disabled={loading || !currentGame}>
                  Assign Roles
                </button>
                <button onClick={handleStartGame} disabled={loading || !currentGame}>
                  Start Game
                </button>
              </div>
            </div>

            <div className="form-section">
              <h3>Available Games</h3>
              <div className="games-list">
                {games.length > 0 ? games.map(game => (
                  <div key={game.id} className="game-item">
                    <h4>{game.name}</h4>
                    <p>Players: {game.currentPlayers}/{game.maxPlayers}</p>
                    <p>Status: {game.state}</p>
                    <p>Storyteller: {game.storyteller?.username}</p>

                    {/* Show current players */}
                    {game.players && game.players.length > 0 && (
                      <div className="players-list">
                        <p><strong>Current Players:</strong></p>
                        <ul>
                          {game.players.map(player => (
                            <li key={player.id}>
                              {player.name} {player.alive ? '🟢' : '💀'}
                              {player.roleId && ` (${player.roleId})`}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <button onClick={() => setCurrentGame(game)}>
                      Select Game
                    </button>
                  </div>
                )) : (
                  <p>No games available</p>
                )}
              </div>
            </div>

            <div className="form-section">
              <h3>Current Game</h3>
              {currentGame ? (
                <div className="game-info">
                  <p><strong>Name:</strong> {currentGame.name}</p>
                  <p><strong>ID:</strong> {currentGame.id}</p>
                  <p><strong>Players:</strong> {currentGame.currentPlayers}/{currentGame.maxPlayers}</p>
                  <p><strong>Status:</strong> {currentGame.state}</p>
                  <p><strong>Roles Assigned:</strong> {currentGame.rolesAssigned ? 'Yes' : 'No'}</p>
                </div>
              ) : (
                <p>No game selected</p>
              )}
            </div>

            <div className="form-section">
              <h3>Players in Current Game</h3>
              <div className="players-list">
                {players.length > 0 ? players.map(player => (
                  <div key={player.id} className="player-item">
                    <p><strong>Name:</strong> {player.name}</p>
                    <p><strong>Position:</strong> {player.position}</p>
                    <p><strong>Alive:</strong> {player.alive ? 'Yes' : 'No'}</p>
                    <p><strong>Role:</strong> {player.role?.name || 'Not assigned'}</p>
                  </div>
                )) : (
                  <p>No players in current game</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Roles Tab */}
        {activeTab === 'roles' && (
          <div className="tab-content">
            <h2>👥 Role System Testing</h2>

            <div className="form-section">
              <h3>Role Actions</h3>
              <div className="button-group">
                <button onClick={handleLoadRoles} disabled={loading}>
                  Load All Roles
                </button>
              </div>
            </div>

            <div className="form-section">
              <h3>Available Roles</h3>
              <div className="roles-grid">
                {roles.length > 0 ? roles.map(role => (
                  <RoleCard
                    key={role.id}
                    role={role}
                    isFlipped={false}
                    onFlip={() => { }}
                  />
                )) : (
                  <p>No roles loaded. Click "Load All Roles" to fetch them.</p>
                )}
              </div>
            </div>

            <div className="form-section">
              <h3>Player Roles (Current Game)</h3>
              <div className="player-roles">
                {players.filter(p => p.role).map(player => (
                  <div key={player.id} className="player-role-item">
                    <h4>{player.name}</h4>
                    <RoleCard
                      role={player.role}
                      isFlipped={true}
                      onFlip={() => { }}
                    />
                  </div>
                ))}
                {players.filter(p => p.role).length === 0 && (
                  <p>No roles assigned to players yet.</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Voting Tab */}
        {activeTab === 'voting' && (
          <div className="tab-content">
            <h2>🗳️ Voting System Testing</h2>

            <div className="form-section">
              <h3>Start Voting</h3>
              <div className="voting-controls">
                <p>Select a player to nominate:</p>
                <div className="players-voting-list">
                  {players.map(player => (
                    <button
                      key={player.id}
                      onClick={() => handleStartVoting(player.id)}
                      disabled={loading || !currentGame}
                      className="nominate-btn"
                    >
                      Nominate {player.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3>Cast Vote</h3>
              <div className="vote-buttons">
                <button
                  onClick={() => handleCastVote('FOR')}
                  disabled={loading || !currentGame}
                  className="vote-btn yes"
                >
                  Vote FOR (Execute)
                </button>
                <button
                  onClick={() => handleCastVote('AGAINST')}
                  disabled={loading || !currentGame}
                  className="vote-btn no"
                >
                  Vote AGAINST
                </button>
                <button
                  onClick={() => handleCastVote('ABSTAIN')}
                  disabled={loading || !currentGame}
                  className="vote-btn abstain"
                >
                  Abstain
                </button>
              </div>
            </div>

            <div className="form-section">
              <h3>Voting Actions</h3>
              <div className="button-group">
                <button onClick={handleEndVoting} disabled={loading || !currentGame}>
                  End Voting
                </button>
              </div>
            </div>

            <div className="form-section">
              <h3>Voting Instructions</h3>
              <div className="instructions">
                <ol>
                  <li>First, start voting by nominating a player</li>
                  <li>Players can then cast their votes (YES/NO)</li>
                  <li>End the voting to see results</li>
                </ol>
                <p><strong>Note:</strong> This is a simplified voting interface for testing the backend API.</p>
              </div>
            </div>
          </div>
        )}

        {/* Timer Tab */}
        {activeTab === 'timer' && (
          <div className="tab-content">
            <h2>⏰ Game Timer Testing</h2>

            <div className="form-section">
              <h3>Timer Controls</h3>
              <div className="timer-controls-section">
                <div className="timer-settings">
                  <label>
                    Timer Duration (seconds):
                    <input
                      type="number"
                      min="10"
                      max="1800"
                      value={timerDuration}
                      onChange={(e) => setTimerDuration(parseInt(e.target.value))}
                    />
                  </label>
                  <label>
                    Phase:
                    <select
                      value={currentPhase}
                      onChange={(e) => setCurrentPhase(e.target.value)}
                    >
                      <option value="day">Day</option>
                      <option value="night">Night</option>
                    </select>
                  </label>
                </div>

                <div className="timer-actions">
                  <button onClick={() => setTimerRunning(!timerRunning)}>
                    {timerRunning ? 'Pause Timer' : 'Start Timer'}
                  </button>
                  <button onClick={() => setTimerRunning(false)}>
                    Stop Timer
                  </button>
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3>Game Timer</h3>
              <div className="timer-display-section">
                <GameTimer
                  initialTime={timerDuration}
                  isRunning={timerRunning}
                  phase={currentPhase}
                  onTimeUp={() => {
                    setTimerRunning(false);
                    showSuccess('Timer finished!');
                  }}
                  onTick={(timeLeft) => {
                    if (timeLeft === 10) {
                      showSuccess('10 seconds remaining!');
                    }
                  }}
                />
              </div>
            </div>

            <div className="form-section">
              <h3>Timer Instructions</h3>
              <div className="instructions">
                <ul>
                  <li>Set the timer duration and phase (day/night)</li>
                  <li>Click "Start Timer" to begin countdown</li>
                  <li>The timer will show different styling for day/night phases</li>
                  <li>You'll get notifications when time is running out</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
