# Multi-stage Dockerfile for Blood on the Clocktower Assistant
# Optimized for fast builds with advanced caching strategies

# Stage 1: Frontend Dependencies Cache Layer
FROM node:20-alpine AS frontend-deps

# Install build dependencies for native modules (if needed)
RUN apk add --no-cache python3 make g++

# Set working directory for frontend
WORKDIR /app/client

# Copy package files for better layer caching
COPY src/client/package*.json ./

# Install dependencies with npm ci for faster, reproducible builds
# Use --prefer-offline and --no-audit for faster installs
RUN npm ci --prefer-offline --no-audit --no-fund

# Stage 2: Build React Frontend
FROM frontend-deps AS frontend-builder

# Copy frontend source code (separate layer for better caching)
COPY src/client/src ./src
COPY src/client/public ./public
COPY src/client/index.html ./
COPY src/client/vite.config.js ./
COPY src/client/eslint.config.js ./

# Debug: Verify assets are copied to build context
RUN ls -la src/ && ls -la src/assets/ || echo "No assets directory found in src"
# Build the React application with optimizations
ENV NODE_ENV=production
RUN npm run build

# Debug: List the contents of the dist directory to verify assets are built
RUN ls -la dist/ && ls -la dist/assets/ || echo "No assets directory found"

# Stage 3: Maven Dependencies Cache Layer
FROM maven:3.9-eclipse-temurin-17-alpine AS maven-deps

# Set working directory for backend
WORKDIR /app/server

# Copy Maven files for dependency caching
COPY src/server/pom.xml ./
COPY src/server/mvnw src/server/mvnw.cmd ./
COPY src/server/.mvn ./.mvn

# Download dependencies (this layer will be cached if pom.xml doesn't change)
# Use --batch-mode for non-interactive builds and --fail-never for better caching
RUN mvn dependency:go-offline -B --fail-never

# Stage 4: Build Spring Boot Backend
FROM maven-deps AS backend-builder

# Copy backend source code
COPY src/server/src ./src

# Build the Spring Boot application with optimizations
# Use parallel builds and skip unnecessary plugins
RUN mvn clean package -DskipTests -B -T 1C \
  -Dmaven.compile.fork=true \
  -Dmaven.javadoc.skip=true \
  -Dmaven.source.skip=true

# Stage 5: Production Runtime
FROM eclipse-temurin:17-jre-alpine AS production

# Install curl for health checks (Alpine version)
RUN apk add --no-cache curl

# Create app user for security
RUN addgroup -g 1001 -S appuser && adduser -S appuser -G appuser -u 1001

# Set working directory
WORKDIR /app

# Copy the built frontend from frontend builder
COPY --from=frontend-builder /app/client/dist ./static

# Debug: List the contents of the static directory to verify assets are copied
RUN ls -la static/ && ls -la static/assets/ || echo "No assets directory found in static"
# Copy the built JAR from backend builder
COPY --from=backend-builder /app/server/target/*.jar app.jar

# Create directory for SQLite database with proper permissions
RUN mkdir -p /app/data && chown -R appuser:appuser /app && chmod -R 755 /app/data

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check with reduced frequency for production
HEALTHCHECK --interval=60s --timeout=10s --start-period=45s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# Environment variables for JVM optimization
ENV SPRING_PROFILES_ACTIVE=production \
  SERVER_PORT=8080 \
  SPRING_DATASOURCE_URL=***************************** \
  JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication"

# Run the application with optimized JVM settings
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]
